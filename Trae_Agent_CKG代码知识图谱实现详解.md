# Trae Agent CKG (Code Knowledge Graph) 实现详解

## 目录

1. [CKG概述](#ckg概述)
2. [核心架构](#核心架构)
3. [数据结构设计](#数据结构设计)
4. [AST解析实现](#ast解析实现)
5. [数据库存储](#数据库存储)
6. [缓存和优化](#缓存和优化)
7. [使用接口](#使用接口)
8. [性能分析](#性能分析)

---

## CKG概述

### 什么是CKG

CKG (Code Knowledge Graph) 是Trae Agent中的代码知识图谱系统，它通过解析项目代码的抽象语法树(AST)，提取函数、类、方法等代码结构信息，并将这些信息存储在SQLite数据库中，为代理提供快速的代码搜索和理解能力。

### 核心功能

1. **代码结构提取**: 自动解析多种编程语言的代码结构
2. **知识图谱构建**: 建立函数、类、方法之间的关系
3. **快速搜索**: 提供高效的代码元素搜索功能
4. **智能缓存**: 基于代码变更的智能缓存机制

### 支持的编程语言

```python
extension_to_language = {
    ".py": "python",
    ".java": "java", 
    ".cpp": "cpp",
    ".hpp": "cpp",
    ".c++": "cpp",
    ".cxx": "cpp",
    ".cc": "cpp",
    ".c": "c",
    ".h": "c",
    ".ts": "typescript",
    ".tsx": "typescript", 
    ".js": "javascript",
    ".jsx": "javascript",
}
```

---

## 核心架构

### 系统架构图

```mermaid
graph TD
    A[代码库] --> B[文件扫描器]
    B --> C[语言识别器]
    C --> D[Tree-sitter解析器]
    D --> E[AST遍历器]
    E --> F[代码结构提取器]
    F --> G[SQLite数据库]
    G --> H[CKG查询接口]
    H --> I[CKG工具]
    
    subgraph "缓存系统"
        J[快照哈希计算]
        K[数据库缓存]
        L[存储信息管理]
    end
    
    B --> J
    J --> K
    K --> G
```

### 核心组件

1. **CKGDatabase**: 核心数据库管理类
2. **CKGTool**: 对外提供的工具接口
3. **FunctionEntry/ClassEntry**: 数据结构定义
4. **AST解析器**: 多语言AST解析实现

---

## 数据结构设计

### 基础数据结构

**FunctionEntry (函数条目)**:
```python
@dataclass
class FunctionEntry:
    name: str                    # 函数名
    file_path: str              # 文件路径
    body: str                   # 函数完整代码
    start_line: int             # 起始行号
    end_line: int               # 结束行号
    parent_function: str | None # 父函数(嵌套函数)
    parent_class: str | None    # 所属类(方法)
```

**ClassEntry (类条目)**:
```python
@dataclass
class ClassEntry:
    name: str           # 类名
    file_path: str      # 文件路径
    body: str           # 类完整代码
    start_line: int     # 起始行号
    end_line: int       # 结束行号
    fields: str | None  # 类字段列表
    methods: str | None # 类方法列表
```

### 数据库表结构

**functions表**:
```sql
CREATE TABLE IF NOT EXISTS functions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    body TEXT NOT NULL,
    start_line INTEGER NOT NULL,
    end_line INTEGER NOT NULL,
    parent_function TEXT,
    parent_class TEXT
)
```

**classes表**:
```sql
CREATE TABLE IF NOT EXISTS classes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    body TEXT NOT NULL,
    fields TEXT,
    methods TEXT,
    start_line INTEGER NOT NULL,
    end_line INTEGER NOT NULL
)
```

---

## AST解析实现

### Tree-sitter集成

CKG使用Tree-sitter作为多语言AST解析器：

```python
from tree_sitter import Node, Parser
from tree_sitter_languages import get_parser

# 懒加载解析器
language_to_parser: dict[str, Parser] = {}
language_parser = get_parser(language)
tree = language_parser.parse(file.read_bytes())
root_node = tree.root_node
```

### Python代码解析

**函数解析**:
```python
def _recursive_visit_python(self, root_node: Node, file_path: str, 
                           parent_class=None, parent_function=None):
    if root_node.type == "function_definition":
        function_name_node = root_node.child_by_field_name("name")
        if function_name_node:
            function_entry = FunctionEntry(
                name=function_name_node.text.decode(),
                file_path=file_path,
                body=root_node.text.decode(),
                start_line=root_node.start_point[0] + 1,
                end_line=root_node.end_point[0] + 1,
            )
            # 设置父级关系
            if parent_class:
                function_entry.parent_class = parent_class.name
            if parent_function:
                function_entry.parent_function = parent_function.name
            
            self._insert_entry(function_entry)
```

**类解析**:
```python
elif root_node.type == "class_definition":
    class_name_node = root_node.child_by_field_name("name")
    if class_name_node:
        class_entry = ClassEntry(
            name=class_name_node.text.decode(),
            file_path=file_path,
            body=root_node.text.decode(),
            start_line=root_node.start_point[0] + 1,
            end_line=root_node.end_point[0] + 1,
        )
        
        # 提取类方法信息
        class_body_node = root_node.child_by_field_name("body")
        if class_body_node:
            class_methods = ""
            for child in class_body_node.children:
                if child.type == "function_definition":
                    method_name_node = child.child_by_field_name("name")
                    if method_name_node:
                        parameters_node = child.child_by_field_name("parameters")
                        method_info = method_name_node.text.decode()
                        if parameters_node:
                            method_info += f"{parameters_node.text.decode()}"
                        class_methods += f"- {method_info}\n"
            
            class_entry.methods = class_methods.strip() if class_methods else None
        
        self._insert_entry(class_entry)
```

### 多语言支持

**Java解析**:
- 支持类声明、方法声明、字段声明
- 提取方法签名和类字段信息

**C++解析**:
- 支持类规范、函数定义、字段声明
- 区分类方法和独立函数

**JavaScript/TypeScript解析**:
- 支持类声明、方法定义、公共字段
- 处理ES6+语法特性

**C解析**:
- 专注于函数定义解析
- 提取函数声明和实现

---

## 数据库存储

### 存储路径管理

```python
# 存储路径配置
LOCAL_STORAGE_PATH = Path.home() / ".trae-agent"
CKG_DATABASE_PATH = LOCAL_STORAGE_PATH / "ckg"
CKG_STORAGE_INFO_FILE = CKG_DATABASE_PATH / "storage_info.json"

# 数据库文件命名
def get_ckg_database_path(codebase_snapshot_hash: str) -> Path:
    return CKG_DATABASE_PATH / f"{codebase_snapshot_hash}.db"
```

### 数据插入流程

```python
def _insert_entry(self, entry: FunctionEntry | ClassEntry) -> None:
    match entry:
        case FunctionEntry():
            self._insert_function(entry)
        case ClassEntry():
            self._insert_class(entry)
    self._db_connection.commit()

def _insert_function(self, entry: FunctionEntry) -> None:
    self._db_connection.execute("""
        INSERT INTO functions (name, file_path, body, start_line, end_line, parent_function, parent_class)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    """, (
        entry.name, entry.file_path, entry.body,
        entry.start_line, entry.end_line,
        entry.parent_function, entry.parent_class,
    ))
```

### 查询接口

```python
def query_function(self, identifier: str, entry_type="function") -> list[FunctionEntry]:
    records = self._db_connection.execute("""
        SELECT name, file_path, body, start_line, end_line, parent_function, parent_class 
        FROM functions WHERE name = ?
    """, (identifier,)).fetchall()
    
    function_entries = []
    for record in records:
        if entry_type == "function" and record[6] is None:  # 独立函数
            function_entries.append(FunctionEntry(*record))
        elif entry_type == "class_method" and record[6] is not None:  # 类方法
            function_entries.append(FunctionEntry(*record))
    
    return function_entries

def query_class(self, identifier: str) -> list[ClassEntry]:
    records = self._db_connection.execute("""
        SELECT name, file_path, body, fields, methods, start_line, end_line 
        FROM classes WHERE name = ?
    """, (identifier,)).fetchall()
    
    return [ClassEntry(*record) for record in records]
```

---

## 缓存和优化

### 智能缓存机制

CKG实现了基于代码变更的智能缓存系统：

**1. 快照哈希计算**:
```python
def get_folder_snapshot_hash(folder_path: Path) -> str:
    # Git仓库: 基于commit hash + 未提交变更
    if is_git_repository(folder_path):
        return get_git_status_hash(folder_path)
    # 非Git: 基于文件元数据
    return get_file_metadata_hash(folder_path)
```

**2. Git状态哈希**:
```python
def get_git_status_hash(folder_path: Path) -> str:
    # 获取当前commit hash
    commit_result = subprocess.run(
        ["git", "rev-parse", "HEAD"], 
        cwd=folder_path, capture_output=True, text=True
    )
    base_hash = commit_result.stdout.strip()
    
    # 检查未提交变更
    status_result = subprocess.run(
        ["git", "status", "--porcelain"],
        cwd=folder_path, capture_output=True, text=True
    )
    
    if not status_result.stdout.strip():
        return f"git-clean-{base_hash}"  # 干净状态
    else:
        # 包含未提交变更的哈希
        uncommitted_hash = hashlib.md5(status_result.stdout.encode()).hexdigest()[:8]
        return f"git-dirty-{base_hash}-{uncommitted_hash}"
```

**3. 文件元数据哈希**:
```python
def get_file_metadata_hash(folder_path: Path) -> str:
    hash_md5 = hashlib.md5()
    for file in folder_path.glob("**/*"):
        if file.is_file() and not file.name.startswith("."):
            stat = file.stat()
            hash_md5.update(file.name.encode())
            hash_md5.update(str(stat.st_mtime).encode())  # 修改时间
            hash_md5.update(str(stat.st_size).encode())   # 文件大小
    return f"metadata-{hash_md5.hexdigest()}"
```

### 缓存重用逻辑

```python
def __init__(self, codebase_path: Path):
    # 读取现有缓存信息
    if CKG_STORAGE_INFO_FILE.exists():
        with open(CKG_STORAGE_INFO_FILE, "r") as f:
            ckg_storage_info = json.load(f)
            existing_hash = ckg_storage_info.get(codebase_path.absolute().as_posix(), "")
    
    current_hash = get_folder_snapshot_hash(codebase_path)
    
    if existing_hash == current_hash:
        # 重用现有数据库
        database_path = get_ckg_database_path(existing_hash)
        self._db_connection = sqlite3.connect(database_path)
    else:
        # 创建新数据库
        if existing_hash:
            old_db_path = get_ckg_database_path(existing_hash)
            if old_db_path.exists():
                old_db_path.unlink()  # 删除旧数据库
        
        database_path = get_ckg_database_path(current_hash)
        self._db_connection = sqlite3.connect(database_path)
        # 创建表结构并构建CKG
        self._construct_ckg()
        
        # 更新缓存信息
        ckg_storage_info[codebase_path.absolute().as_posix()] = current_hash
        with open(CKG_STORAGE_INFO_FILE, "w") as f:
            json.dump(ckg_storage_info, f)
```

### 过期清理机制

```python
CKG_DATABASE_EXPIRY_TIME = 60 * 60 * 24 * 7  # 1周过期

def clear_older_ckg():
    """删除超过1周的CKG数据库文件"""
    for file in CKG_DATABASE_PATH.glob("**/*"):
        if (file.is_file() and file.name.endswith(".db") and 
            file.stat().st_mtime < datetime.now().timestamp() - CKG_DATABASE_EXPIRY_TIME):
            try:
                file.unlink()
            except Exception as e:
                print(f"删除过期CKG数据库失败 - {file}: {e}")
```

---

## 使用接口

### CKG工具接口

CKG通过`CKGTool`类对外提供服务：

```python
class CKGTool(Tool):
    def __init__(self):
        # 缓存多个代码库的CKG数据库
        self._ckg_databases: dict[Path, CKGDatabase] = {}
    
    async def execute(self, arguments: ToolCallArguments) -> ToolExecResult:
        command = arguments.get("command")      # search_function/search_class/search_class_method
        path = arguments.get("path")            # 代码库路径
        identifier = arguments.get("identifier") # 搜索标识符
        print_body = arguments.get("print_body", True)  # 是否打印代码体
        
        # 获取或创建CKG数据库
        ckg_database = self._ckg_databases.get(codebase_path)
        if ckg_database is None:
            ckg_database = CKGDatabase(codebase_path)
            self._ckg_databases[codebase_path] = ckg_database
        
        # 执行搜索命令
        match command:
            case "search_function":
                return self._search_function(ckg_database, identifier, print_body)
            case "search_class":
                return self._search_class(ckg_database, identifier, print_body)
            case "search_class_method":
                return self._search_class_method(ckg_database, identifier, print_body)
```

### 搜索功能实现

**函数搜索**:
```python
def _search_function(self, ckg_database: CKGDatabase, identifier: str, print_body: bool = True) -> str:
    entries = ckg_database.query_function(identifier, entry_type="function")
    
    if len(entries) == 0:
        return f"No functions named {identifier} found."
    
    output = f"Found {len(entries)} functions named {identifier}:\n"
    for i, entry in enumerate(entries, 1):
        output += f"{i}. {entry.file_path}:{entry.start_line}-{entry.end_line}\n"
        if print_body:
            output += f"{entry.body}\n\n"
        
        # 防止输出过长
        if len(output) > MAX_RESPONSE_LEN:
            output = output[:MAX_RESPONSE_LEN] + f"\n<response clipped> {len(entries) - i} more entries not shown"
            break
    
    return output
```

**类搜索**:
```python
def _search_class(self, ckg_database: CKGDatabase, identifier: str, print_body: bool = True) -> str:
    entries = ckg_database.query_class(identifier)
    
    output = f"Found {len(entries)} classes named {identifier}:\n"
    for i, entry in enumerate(entries, 1):
        output += f"{i}. {entry.file_path}:{entry.start_line}-{entry.end_line}\n"
        
        # 显示类字段和方法
        if entry.fields:
            output += f"Fields:\n{entry.fields}\n"
        if entry.methods:
            output += f"Methods:\n{entry.methods}\n"
        
        if print_body:
            output += f"{entry.body}\n\n"
    
    return output
```

**类方法搜索**:
```python
def _search_class_method(self, ckg_database: CKGDatabase, identifier: str, print_body: bool = True) -> str:
    entries = ckg_database.query_function(identifier, entry_type="class_method")
    
    output = f"Found {len(entries)} class methods named {identifier}:\n"
    for i, entry in enumerate(entries, 1):
        output += f"{i}. {entry.file_path}:{entry.start_line}-{entry.end_line} within class {entry.parent_class}\n"
        if print_body:
            output += f"{entry.body}\n\n"
    
    return output
```

---

## 性能分析

### 构建性能

**时间复杂度**: O(n*m)
- n: 文件数量
- m: 平均文件大小

**空间复杂度**: O(k)
- k: 代码结构数量(函数+类)

### 查询性能

**数据库索引**: 目前使用简单的表扫描，可优化为：
```sql
CREATE INDEX idx_functions_name ON functions(name);
CREATE INDEX idx_classes_name ON classes(name);
```

**内存使用**: 
- 每个CKGDatabase实例维护一个SQLite连接
- 多个代码库共享CKGTool实例，节省内存

### 已知限制

1. **子目录重建**: 当索引子目录时会重新构建整个CKG
2. **增量更新**: 目前不支持增量更新，只能全量重建
3. **JavaScript/TypeScript**: AST解析不完整，缺少匿名函数、箭头函数等
4. **索引准确性**: CKG可能无法找到所有函数或类

### 优化建议

1. **增量更新**: 实现基于文件变更的增量更新
2. **并行解析**: 使用多线程并行解析文件
3. **索引优化**: 添加数据库索引提升查询性能
4. **内存优化**: 实现CKG数据库的LRU缓存
5. **解析完整性**: 改进JavaScript/TypeScript解析器

---

---

## 实际使用示例

### 命令行使用

```bash
# 搜索函数
trae-cli run "使用CKG搜索项目中名为'parse_config'的函数"

# 搜索类
trae-cli run "查找项目中的'DatabaseManager'类的定义和方法"

# 搜索类方法
trae-cli run "找到所有名为'execute'的类方法"
```

### 代码中的使用

```python
from trae_agent.tools.ckg_tool import CKGTool
from pathlib import Path

# 创建CKG工具实例
ckg_tool = CKGTool()

# 搜索函数
result = await ckg_tool.execute({
    "command": "search_function",
    "path": "/path/to/project",
    "identifier": "main",
    "print_body": True
})

print(result.output)
# 输出:
# Found 2 functions named main:
# 1. /path/to/project/src/main.py:15-25
# def main():
#     """Main entry point"""
#     app = create_app()
#     app.run()
#
# 2. /path/to/project/tests/test_main.py:8-12
# def main():
#     """Test main function"""
#     pass
```

### LLM Agent使用场景

**场景1: 理解代码结构**
```
Agent: 我需要了解这个项目的主要类结构
CKG: 搜索所有类 -> 返回类列表和方法概览
Agent: 基于类信息制定修改策略
```

**场景2: 定位bug相关代码**
```
Agent: 错误信息提到'validate_input'函数
CKG: 搜索validate_input函数 -> 返回函数定义和位置
Agent: 分析函数代码，定位bug原因
```

**场景3: 重构代码**
```
Agent: 需要重构'UserManager'类
CKG: 搜索UserManager类 -> 返回类定义、字段、方法
Agent: 基于类结构信息进行重构
```

---

## 技术实现细节

### Tree-sitter集成深度分析

**解析器初始化**:
```python
def get_parser(language: str) -> Parser:
    """获取指定语言的Tree-sitter解析器"""
    parser = Parser()
    # tree_sitter_languages库提供预编译的语言解析器
    language_lib = tree_sitter_languages.get_language(language)
    parser.set_language(language_lib)
    return parser
```

**AST节点遍历策略**:
```python
def _recursive_visit_python(self, root_node: Node, file_path: str,
                           parent_class=None, parent_function=None):
    """递归遍历Python AST节点"""

    # 处理当前节点
    if root_node.type == "function_definition":
        self._process_function_node(root_node, file_path, parent_class, parent_function)
    elif root_node.type == "class_definition":
        self._process_class_node(root_node, file_path, parent_class)

    # 递归处理子节点
    for child in root_node.children:
        self._recursive_visit_python(child, file_path, parent_class, parent_function)
```

**节点信息提取**:
```python
def _extract_node_info(self, node: Node) -> dict:
    """提取AST节点的关键信息"""
    return {
        "text": node.text.decode(),           # 节点文本内容
        "type": node.type,                   # 节点类型
        "start_point": node.start_point,     # 起始位置(行,列)
        "end_point": node.end_point,         # 结束位置(行,列)
        "start_byte": node.start_byte,       # 起始字节位置
        "end_byte": node.end_byte,           # 结束字节位置
    }
```

### 多语言解析差异处理

**Python特殊处理**:
```python
# 装饰器函数处理
if child.type == "decorated_definition":
    function_definition_node = child.child_by_field_name("definition")
elif child.type == "function_definition":
    function_definition_node = child

# 方法参数和返回类型提取
parameters_node = function_definition_node.child_by_field_name("parameters")
return_type_node = child.child_by_field_name("return_type")

method_info = method_name_node.text.decode()
if parameters_node:
    method_info += f"{parameters_node.text.decode()}"
if return_type_node:
    method_info += f" -> {return_type_node.text.decode()}"
```

**Java特殊处理**:
```python
# Java方法声明处理
if child.type == "method_declaration":
    method_builder = ""
    for method_property in child.children:
        if method_property.type == "block":
            break  # 不包含方法体
        method_builder += f"{method_property.text.decode()} "
    method_builder = method_builder.strip()
    class_methods += f"- {method_builder}\n"

# Java字段声明处理
if child.type == "field_declaration":
    class_fields += f"- {child.text.decode()}\n"
```

**C++特殊处理**:
```python
# C++函数声明器处理
function_declarator_node = root_node.child_by_field_name("declarator")
if function_declarator_node:
    function_name_node = function_declarator_node.child_by_field_name("declarator")

# C++字段和方法区分
if child.type == "field_declaration":
    child_is_property = True
    for child_property in child.children:
        if child_property.type == "function_declarator":
            child_is_property = False
            break

    if child_is_property:
        class_fields += f"- {child.text.decode()}\n"
    else:
        class_methods += f"- {child.text.decode()}\n"
```

### 数据库操作优化

**批量插入优化**:
```python
def _batch_insert_entries(self, entries: list[FunctionEntry | ClassEntry]) -> None:
    """批量插入条目，提升性能"""
    function_entries = [e for e in entries if isinstance(e, FunctionEntry)]
    class_entries = [e for e in entries if isinstance(e, ClassEntry)]

    if function_entries:
        self._db_connection.executemany("""
            INSERT INTO functions (name, file_path, body, start_line, end_line, parent_function, parent_class)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, [(e.name, e.file_path, e.body, e.start_line, e.end_line, e.parent_function, e.parent_class)
              for e in function_entries])

    if class_entries:
        self._db_connection.executemany("""
            INSERT INTO classes (name, file_path, body, fields, methods, start_line, end_line)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, [(e.name, e.file_path, e.body, e.fields, e.methods, e.start_line, e.end_line)
              for e in class_entries])

    self._db_connection.commit()
```

**查询优化**:
```python
def query_function_with_context(self, identifier: str, context_lines: int = 5) -> list[dict]:
    """查询函数并包含上下文信息"""
    records = self._db_connection.execute("""
        SELECT name, file_path, body, start_line, end_line, parent_function, parent_class
        FROM functions
        WHERE name LIKE ? OR body LIKE ?
        ORDER BY name
    """, (f"%{identifier}%", f"%{identifier}%")).fetchall()

    results = []
    for record in records:
        # 读取文件获取上下文
        file_path = Path(record[1])
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            start_line = max(0, record[3] - context_lines - 1)
            end_line = min(len(lines), record[4] + context_lines)
            context = ''.join(lines[start_line:end_line])

            results.append({
                'function': FunctionEntry(*record),
                'context': context,
                'context_start_line': start_line + 1
            })

    return results
```

### 错误处理和容错机制

**文件解析错误处理**:
```python
def _safe_parse_file(self, file_path: Path, language: str) -> Node | None:
    """安全解析文件，处理编码和语法错误"""
    try:
        # 尝试多种编码
        for encoding in ['utf-8', 'gbk', 'latin1']:
            try:
                content = file_path.read_text(encoding=encoding)
                break
            except UnicodeDecodeError:
                continue
        else:
            print(f"无法解码文件: {file_path}")
            return None

        # 解析AST
        parser = self._get_parser(language)
        tree = parser.parse(content.encode('utf-8'))

        if tree.root_node.has_error:
            print(f"AST解析错误: {file_path}")
            return None

        return tree.root_node

    except Exception as e:
        print(f"解析文件失败 {file_path}: {e}")
        return None
```

**数据库连接错误处理**:
```python
def _safe_db_operation(self, operation_func, *args, **kwargs):
    """安全执行数据库操作"""
    max_retries = 3
    for attempt in range(max_retries):
        try:
            return operation_func(*args, **kwargs)
        except sqlite3.OperationalError as e:
            if "database is locked" in str(e) and attempt < max_retries - 1:
                time.sleep(0.1 * (2 ** attempt))  # 指数退避
                continue
            else:
                raise
        except Exception as e:
            print(f"数据库操作失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt == max_retries - 1:
                raise
```

---

## 扩展和定制

### 添加新语言支持

**1. 扩展语言映射**:
```python
extension_to_language.update({
    ".go": "go",
    ".rs": "rust",
    ".php": "php",
    ".rb": "ruby",
})
```

**2. 实现语言特定解析器**:
```python
def _recursive_visit_go(self, root_node: Node, file_path: str,
                       parent_class=None, parent_function=None):
    """Go语言AST遍历实现"""
    if root_node.type == "function_declaration":
        # 处理Go函数声明
        self._process_go_function(root_node, file_path)
    elif root_node.type == "type_declaration":
        # 处理Go类型声明(结构体)
        self._process_go_struct(root_node, file_path)

    # 递归处理子节点
    for child in root_node.children:
        self._recursive_visit_go(child, file_path, parent_class, parent_function)
```

**3. 在构建流程中集成**:
```python
def _construct_ckg(self) -> None:
    # 在语言匹配中添加新语言
    match language:
        case "python":
            self._recursive_visit_python(root_node, file.absolute().as_posix())
        case "go":
            self._recursive_visit_go(root_node, file.absolute().as_posix())
        # ... 其他语言
```

### 自定义查询功能

**模糊搜索**:
```python
def fuzzy_search_function(self, pattern: str, threshold: float = 0.6) -> list[FunctionEntry]:
    """基于相似度的模糊搜索"""
    from difflib import SequenceMatcher

    all_functions = self._db_connection.execute(
        "SELECT name, file_path, body, start_line, end_line, parent_function, parent_class FROM functions"
    ).fetchall()

    matches = []
    for record in all_functions:
        similarity = SequenceMatcher(None, pattern.lower(), record[0].lower()).ratio()
        if similarity >= threshold:
            matches.append((similarity, FunctionEntry(*record)))

    # 按相似度排序
    matches.sort(key=lambda x: x[0], reverse=True)
    return [match[1] for match in matches]
```

**语义搜索**:
```python
def semantic_search(self, query: str, top_k: int = 10) -> list[dict]:
    """基于代码语义的搜索(需要集成embedding模型)"""
    # 这里可以集成CodeBERT等代码embedding模型
    # 1. 将查询转换为embedding
    # 2. 计算与代码片段的相似度
    # 3. 返回最相似的结果
    pass
```

### 性能监控和分析

**构建性能监控**:
```python
import time
from dataclasses import dataclass

@dataclass
class CKGBuildStats:
    total_files: int = 0
    processed_files: int = 0
    total_functions: int = 0
    total_classes: int = 0
    build_time: float = 0.0
    errors: list[str] = None

def _construct_ckg_with_stats(self) -> CKGBuildStats:
    """构建CKG并收集统计信息"""
    stats = CKGBuildStats(errors=[])
    start_time = time.time()

    for file in self._codebase_path.glob("**/*"):
        if self._should_process_file(file):
            stats.total_files += 1
            try:
                self._process_file(file)
                stats.processed_files += 1
            except Exception as e:
                stats.errors.append(f"{file}: {e}")

    stats.build_time = time.time() - start_time
    stats.total_functions = self._count_functions()
    stats.total_classes = self._count_classes()

    return stats
```

**查询性能分析**:
```python
def analyze_query_performance(self):
    """分析查询性能"""
    import sqlite3

    # 启用查询计划分析
    self._db_connection.execute("PRAGMA query_only = ON")

    test_queries = [
        "SELECT * FROM functions WHERE name = 'main'",
        "SELECT * FROM classes WHERE name LIKE '%Manager%'",
        "SELECT COUNT(*) FROM functions GROUP BY parent_class",
    ]

    for query in test_queries:
        # 分析查询计划
        plan = self._db_connection.execute(f"EXPLAIN QUERY PLAN {query}").fetchall()
        print(f"Query: {query}")
        print(f"Plan: {plan}")

        # 测量执行时间
        start = time.time()
        self._db_connection.execute(query).fetchall()
        duration = time.time() - start
        print(f"Duration: {duration:.4f}s\n")
```

---

## 总结

Trae Agent的CKG系统是一个功能强大的代码知识图谱实现，具有以下特点：

### 优势

1. **多语言支持**: 支持6种主流编程语言
2. **智能缓存**: 基于Git状态的智能缓存机制
3. **高效存储**: 使用SQLite提供快速查询
4. **易于集成**: 通过工具接口无缝集成到Agent工作流
5. **可扩展性**: 清晰的架构便于添加新语言和功能

### 应用价值

1. **代码理解**: 帮助Agent快速理解项目结构
2. **精确定位**: 快速找到相关函数和类
3. **重构支持**: 为代码重构提供结构信息
4. **Bug修复**: 协助定位和修复代码问题

### 技术创新

1. **AST深度解析**: 不仅提取名称，还包含完整代码和关系
2. **增量缓存**: 避免重复构建，提升效率
3. **多维查询**: 支持函数、类、方法的分类查询
4. **容错机制**: 处理各种解析和存储错误

CKG系统为Trae Agent提供了强大的代码理解能力，是实现智能代码操作的重要基础设施。

*文档最后更新时间: 2025年1月15日*
*版本: v1.0.0*
