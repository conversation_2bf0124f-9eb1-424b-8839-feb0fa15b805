name: Pre-commit

on:
  pull_request:
  push:
    branches:
      - main

jobs:
  pre-commit:

    if: github.repository == 'bytedance/trae-agent'
    runs-on: ubuntu-latest
    name: Pre-commit checks

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.12'

    - name: Install uv
      uses: astral-sh/setup-uv@v6

    - name: Create virtual environment and install dependencies
      run: |
        make uv-sync

    - name: Run pre-commit hooks
      run: |
        source .venv/bin/activate
        make uv-pre-commit
