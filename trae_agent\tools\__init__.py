# Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""Tools module for Trae Agent."""

from typing import Type

from .base import Tool, Too<PERSON><PERSON><PERSON>, ToolExecutor, ToolResult
from .bash_tool import BashTool
from .ckg_tool import C<PERSON><PERSON><PERSON><PERSON>
from .edit_tool import TextE<PERSON>or<PERSON>ool
from .json_edit_tool import JSONE<PERSON>T<PERSON>
from .sequential_thinking_tool import SequentialThinkingTool
from .task_done_tool import TaskDoneTool

__all__ = [
    "Tool",
    "ToolResult",
    "ToolCall",
    "ToolExecutor",
    "BashTool",
    "TextEditorTool",
    "JSONEditTool",
    "SequentialThinkingTool",
    "TaskDoneTool",
    "CKGTool",
]

tools_registry: dict[str, Type[Tool]] = {
    "bash": BashTool,
    "str_replace_based_edit_tool": TextEditorTool,
    "json_edit_tool": JSONEditTool,
    "sequentialthinking": SequentialThinkingTool,
    "task_done": TaskDoneTool,
    "ckg": C<PERSON><PERSON><PERSON><PERSON>,
}
