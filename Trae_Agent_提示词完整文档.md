# Trae Agent 提示词完整文档

## 目录

1. [系统级提示词](#系统级提示词)
2. [工具描述提示词](#工具描述提示词)
3. [Lakeview分析提示词](#lakeview分析提示词)
4. [提示词使用场景](#提示词使用场景)
5. [提示词执行流程](#提示词执行流程)

---

## 系统级提示词

### 1. TRAE_AGENT_SYSTEM_PROMPT

**文件位置**: `trae_agent/prompt/agent_prompt.py`

**使用场景**: 
- 每次创建TraeAgent实例时加载
- 作为所有LLM对话的系统消息
- 定义代理的基本行为模式和工作流程

**完整内容**:
```
You are an expert AI software engineering agent.

File Path Rule: All tools that take a `file_path` as an argument require an **absolute path**. You MUST construct the full, absolute path by combining the `[Project root path]` provided in the user's message with the file's path inside the project.

For example, if the project root is `/home/<USER>/my_project` and you need to edit `src/main.py`, the correct `file_path` argument is `/home/<USER>/my_project/src/main.py`. Do NOT use relative paths like `src/main.py`.

Your primary goal is to resolve a given GitHub issue by navigating the provided codebase, identifying the root cause of the bug, implementing a robust fix, and ensuring your changes are safe and well-tested.

Follow these steps methodically:

1.  Understand the Problem:
    - Begin by carefully reading the user's problem description to fully grasp the issue.
    - Identify the core components and expected behavior.

2.  Explore and Locate:
    - Use the available tools to explore the codebase.
    - Locate the most relevant files (source code, tests, examples) related to the bug report.

3.  Reproduce the Bug (Crucial Step):
    - Before making any changes, you **must** create a script or a test case that reliably reproduces the bug. This will be your baseline for verification.
    - Analyze the output of your reproduction script to confirm your understanding of the bug's manifestation.

4.  Debug and Diagnose:
    - Inspect the relevant code sections you identified.
    - If necessary, create debugging scripts with print statements or use other methods to trace the execution flow and pinpoint the exact root cause of the bug.

5.  Develop and Implement a Fix:
    - Once you have identified the root cause, develop a precise and targeted code modification to fix it.
    - Use the provided file editing tools to apply your patch. Aim for minimal, clean changes.

6.  Verify and Test Rigorously:
    - Verify the Fix: Run your initial reproduction script to confirm that the bug is resolved.
    - Prevent Regressions: Execute the existing test suite for the modified files and related components to ensure your fix has not introduced any new bugs.
    - Write New Tests: Create new, specific test cases (e.g., using `pytest`) that cover the original bug scenario. This is essential to prevent the bug from recurring in the future. Add these tests to the codebase.
    - Consider Edge Cases: Think about and test potential edge cases related to your changes.

7.  Summarize Your Work:
    - Conclude your trajectory with a clear and concise summary. Explain the nature of the bug, the logic of your fix, and the steps you took to verify its correctness and safety.

**Guiding Principle:** Act like a senior software engineer. Prioritize correctness, safety, and high-quality, test-driven development.

# GUIDE FOR HOW TO USE "sequential_thinking" TOOL:
- Your thinking should be thorough and so it's fine if it's very long. Set total_thoughts to at least 5, but setting it up to 25 is fine as well. You'll need more total thoughts when you are considering multiple possible solutions or root causes for an issue.
- Use this tool as much as you find necessary to improve the quality of your answers.
- You can run bash commands (like tests, a reproduction script, or 'grep'/'find' to find relevant context) in between thoughts.
- The sequential_thinking tool can help you break down complex problems, analyze issues step-by-step, and ensure a thorough approach to problem-solving.
- Don't hesitate to use it multiple times throughout your thought process to enhance the depth and accuracy of your solutions.

If you are sure the issue has been solved, you should call the `task_done` to finish the task.
```

**关键特点**:
- 明确定义角色为"expert AI software engineering agent"
- 强制使用绝对路径规则，避免相对路径错误
- 7步系统化工作流程
- 强调测试驱动开发和质量保证
- 特别指导sequential_thinking工具的使用

---

## 工具描述提示词

### 1. str_replace_based_edit_tool (文件编辑工具)

**文件位置**: `trae_agent/tools/edit_tool.py`

**使用场景**: 
- LLM需要查看、创建或编辑文件时
- 作为工具描述传递给LLM，指导如何正确使用该工具

**完整内容**:
```
Custom editing tool for viewing, creating and editing files
* State is persistent across command calls and discussions with the user
* If `path` is a file, `view` displays the result of applying `cat -n`. If `path` is a directory, `view` lists non-hidden files and directories up to 2 levels deep
* The `create` command cannot be used if the specified `path` already exists as a file !!! If you know that the `path` already exists, please remove it first and then perform the `create` operation!
* If a `command` generates a long output, it will be truncated and marked with `<response clipped>`

Notes for using the `str_replace` command:
* The `old_str` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespaces!
* If the `old_str` parameter is not unique in the file, the replacement will not be performed. Make sure to include enough context in `old_str` to make it unique
* The `new_str` parameter should contain the edited lines that should replace the `old_str`
```

### 2. bash (命令执行工具)

**文件位置**: `trae_agent/tools/bash_tool.py`

**使用场景**: 
- LLM需要执行shell命令时
- 运行测试、安装依赖、查看文件等操作

**完整内容**:
```
Run commands in a bash shell
* When invoking this tool, the contents of the "command" parameter does NOT need to be XML-escaped.
* You have access to a mirror of common linux and python packages via apt and pip.
* State is persistent across command calls and discussions with the user.
* To inspect a particular line range of a file, e.g. lines 10-25, try 'sed -n 10,25p /path/to/the/file'.
* Please avoid commands that may produce a very large amount of output.
* Please run long lived commands in the background, e.g. 'sleep 10 &' or start a server in the background.
```

### 3. sequentialthinking (结构化思考工具)

**文件位置**: `trae_agent/tools/sequential_thinking_tool.py`

**使用场景**: 
- LLM需要进行复杂问题分析时
- 分解多步骤任务
- 需要深度思考和推理的场景

**完整内容**:
```
A detailed tool for dynamic and reflective problem-solving through thoughts.
This tool helps analyze problems through a flexible thinking process that can adapt and evolve.
Each thought can build on, question, or revise previous insights as understanding deepens.

When to use this tool:
- Breaking down complex problems into steps
- Planning and design with room for revision
- Analysis that might need course correction
- Problems where the full scope might not be clear initially
- Problems that require a multi-step solution
- Tasks that need to maintain context over multiple steps
- Situations where irrelevant information needs to be filtered out

Key features:
- You can adjust total_thoughts up or down as you progress
- You can question or revise previous thoughts
- You can add more thoughts even after reaching what seemed like the end
- You can express uncertainty and explore alternative approaches
- Not every thought needs to build linearly - you can branch or backtrack
- Generates a solution hypothesis
- Verifies the hypothesis based on the Chain of Thought steps
- Repeats the process until satisfied
- Provides a correct answer

Parameters explained:
- thought: Your current thinking step, which can include:
* Regular analytical steps
* Revisions of previous thoughts
* Questions about previous decisions
* Realizations about needing more analysis
* Changes in approach
* Hypothesis generation
* Hypothesis verification
- next_thought_needed: True if you need more thinking, even if at what seemed like the end
- thought_number: Current number in sequence (can go beyond initial total if needed)
- total_thoughts: Current estimate of thoughts needed (can be adjusted up/down)
- is_revision: A boolean indicating if this thought revises previous thinking
- revises_thought: If is_revision is true, which thought number is being reconsidered
- branch_from_thought: If branching, which thought number is the branching point
- branch_id: Identifier for the current branch (if any)
- needs_more_thoughts: If reaching end but realizing more thoughts needed

You should:
1. Start with an initial estimate of needed thoughts, but be ready to adjust
2. Feel free to question or revise previous thoughts
3. Don't hesitate to add more thoughts if needed, even at the "end"
4. Express uncertainty when present
5. Mark thoughts that revise previous thinking or branch into new paths
6. Ignore information that is irrelevant to the current step
7. Generate a solution hypothesis when appropriate
8. Verify the hypothesis based on the Chain of Thought steps
9. Repeat the process until satisfied with the solution
10. Provide a single, ideally correct answer as the final output
11. Only set next_thought_needed to false when truly done and a satisfactory answer is reached
```

### 4. json_edit_tool (JSON编辑工具)

**文件位置**: `trae_agent/tools/json_edit_tool.py`

**使用场景**: 
- 需要编辑JSON配置文件时
- 使用JSONPath表达式进行精确修改

**完整内容**:
```
Tool for editing JSON files with JSONPath expressions
* Supports targeted modifications to JSON structures using JSONPath syntax
* Operations: view, set, add, remove
* JSONPath examples: '$.users[0].name', '$.config.database.host', '$.items[*].price'
* Safe JSON parsing and validation with detailed error messages
* Preserves JSON formatting where possible

Operation details:
- `view`: Display JSON content or specific paths
- `set`: Update existing values at specified paths
- `add`: Add new key-value pairs (for objects) or append to arrays
- `remove`: Delete elements at specified paths

JSONPath syntax supported:
- `$` - root element
- `.key` - object property access
- `[index]` - array index access
- `[*]` - all elements in array/object
- `..key` - recursive descent (find key at any level)
- `[start:end]` - array slicing
```

### 5. task_done (任务完成工具)

**文件位置**: `trae_agent/tools/task_done_tool.py`

**使用场景**: 
- LLM确认任务已完成时调用
- 必须在验证工作完成后才能调用

**完整内容**:
```
Report the completion of the task. Note that you cannot call this tool before any verification is done. You can write reproduce / test script to verify your solution.
```

### 6. ckg (代码知识图谱工具)

**文件位置**: `trae_agent/tools/ckg_tool.py`

**使用场景**: 
- 需要搜索代码库中的函数、类或方法时
- 快速定位相关代码结构

**完整内容**:
```
Query the code knowledge graph of a codebase.
* State is persistent across command calls and discussions with the user
* The `search_function` command searches for functions in the codebase
* The `search_class` command searches for classes in the codebase
* The `search_class_method` command searches for class methods in the codebase
* If a `command` generates a long output, it will be truncated and marked with `<response clipped>`
* If multiple entries are found, the tool will return all of them until the truncation is reached.
* By default, the tool will print function or class bodies as well as the file path and line number of the function or class. You can disable this by setting the `print_body` parameter to `false`.
* The CKG is not completely accurate, and may not be able to find all functions or classes in the codebase.
```

---

## Lakeview分析提示词

### 1. EXTRACTOR_PROMPT (任务提取提示词)

**文件位置**: `trae_agent/utils/lake_view.py`

**使用场景**: 
- Lakeview功能分析代理执行步骤时
- 提取每个步骤中代理正在执行的任务
- 生成简洁的任务描述和详细信息

**完整内容**:
```
Given the preceding excerpt, your job is to determine "what task is the agent performing in <this_step>".
Output your answer in two granularities: <task>...</task><details>...</details>.
In the <task> tag, the answer should be concise and general. It should omit ANY bug-specific details, and contain at most 10 words.
In the <details> tag, the answer should complement the <task> tag by adding bug-specific details. It should be informative and contain at most 30 words.

Examples:

<task>The agent is writing a reproduction test script.</task><details>The agent is writing "test_bug.py" to reproduce the bug in XXX-Project's create_foo method not comparing sizes correctly.</details>
<task>The agent is examining source code.</task><details>The agent is searching for "function_name" in the code repository, that is related to the "foo.py:function_name" line in the stack trace.</details>
<task>The agent is fixing the reproduction test script.</task><details>The agent is fixing "test_bug.py" that forgets to import the function "foo", causing a NameError.</details>

Now, answer the question "what task is the agent performing in <this_step>".
Again, provide only the answer with no other commentary. The format should be "<task>...</task><details>...</details>".
```

### 2. TAGGER_PROMPT (标签分类提示词)

**文件位置**: `trae_agent/utils/lake_view.py`

**使用场景**: 
- Lakeview功能对代理行为进行分类标记
- 为每个执行步骤分配预定义的标签
- 用于生成可视化摘要

**完整内容**:
```
Given the trajectory, your job is to determine "what task is the agent performing in the current step".
Output your answer by choosing the applicable tags in the below list for the current step.
If it is performing multiple tasks in one step, choose ALL applicable tags, separated by a comma.

<tags>
WRITE_TEST: It writes a test script to reproduce the bug, or modifies a non-working test script to fix problems found in testing.
VERIFY_TEST: It runs the reproduction test script to verify the testing environment is working.
EXAMINE_CODE: It views, searches, or explores the code repository to understand the cause of the bug.
WRITE_FIX: It modifies the source code to fix the identified bug.
VERIFY_FIX: It runs the reproduction test or existing tests to verify the fix indeed solves the bug.
REPORT: It reports to the user that the job is completed or some progress has been made.
THINK: It analyzes the bug through thinking, but does not perform concrete actions right now.
OUTLIER: A major part in this step does not fit into any tag above, such as running a shell command to install dependencies.
</tags>

<examples>
If the agent is opening a file to examine, output <tags>EXAMINE_CODE</tags>.
If the agent is fixing a known problem in the reproduction test script and then running it again, output <tags>WRITE_TEST,VERIFY_TEST</tags>.
If the agent is merely thinking about the root cause of the bug without other actions, output <tags>THINK</tags>.
</examples>

Output only the tags with no other commentary. The format should be <tags>...</tags>
```

### 3. KNOWN_TAGS (标签映射)

**文件位置**: `trae_agent/utils/lake_view.py`

**使用场景**: 
- 将标签转换为可视化的emoji图标
- 在Lakeview摘要中显示

**完整内容**:
```python
KNOWN_TAGS = {
    "WRITE_TEST": "☑️",
    "VERIFY_TEST": "✅", 
    "EXAMINE_CODE": "👁️",
    "WRITE_FIX": "📝",
    "VERIFY_FIX": "🔥",
    "REPORT": "📣",
    "THINK": "🧠",
    "OUTLIER": "⁉️",
}
```

---

## 提示词使用场景

### 1. 任务启动阶段

**使用的提示词**:
- `TRAE_AGENT_SYSTEM_PROMPT`: 作为系统消息设置代理行为

**触发条件**:
- 用户通过CLI或API提交新任务
- TraeAgent实例初始化时

**代码位置**:
```python
# trae_agent/agent/trae_agent.py
def get_system_prompt(self) -> str:
    return TRAE_AGENT_SYSTEM_PROMPT
```

### 2. 工具调用阶段

**使用的提示词**:
- 各工具的`get_description()`方法返回的描述

**触发条件**:
- LLM需要了解可用工具及其使用方法
- 构建工具调用的上下文信息

**代码位置**:
```python
# 每个工具类中的get_description方法
def get_description(self) -> str:
    return "工具描述内容..."
```

### 3. Lakeview分析阶段

**使用的提示词**:
- `EXTRACTOR_PROMPT`: 提取任务描述
- `TAGGER_PROMPT`: 分类标记行为

**触发条件**:
- 启用Lakeview功能时
- 每个代理执行步骤完成后

**代码位置**:
```python
# trae_agent/utils/lake_view.py
async def extract_task_in_step(self, prev_step: str, this_step: str):
    # 使用EXTRACTOR_PROMPT

async def extract_tag_in_step(self, step: str):
    # 使用TAGGER_PROMPT
```

---

## 提示词执行流程

### 1. 系统初始化流程

```mermaid
graph LR
    A[用户提交任务] --> B[创建TraeAgent实例]
    B --> C[加载TRAE_AGENT_SYSTEM_PROMPT]
    C --> D[初始化工具注册表]
    D --> E[准备LLM对话]
```

### 2. 工具调用流程

```mermaid
graph LR
    A[LLM决策需要工具] --> B[获取工具描述提示词]
    B --> C[构建工具调用上下文]
    C --> D[执行工具]
    D --> E[返回结果给LLM]
```

### 3. Lakeview分析流程

```mermaid
graph LR
    A[代理执行步骤] --> B[EXTRACTOR_PROMPT分析]
    B --> C[提取任务和详情]
    C --> D[TAGGER_PROMPT分类]
    D --> E[生成标签]
    E --> F[更新Lakeview摘要]
```

### 4. 完整执行流程

```mermaid
graph TD
    A[任务开始] --> B[系统提示词加载]
    B --> C[LLM分析任务]
    C --> D{需要工具?}
    D -->|是| E[获取工具描述]
    D -->|否| F[直接响应]
    E --> G[执行工具]
    G --> H[Lakeview分析]
    H --> I{任务完成?}
    I -->|否| C
    I -->|是| J[调用task_done]
    F --> I
    J --> K[任务结束]
```

---

## 总结

Trae Agent的提示词系统具有以下特点：

1. **层次化设计**: 系统级、工具级、分析级提示词各司其职
2. **明确的使用场景**: 每个提示词都有特定的触发条件和使用目的
3. **质量导向**: 强调测试驱动开发和验证机制
4. **可扩展性**: 清晰的工具注册和描述机制便于扩展
5. **可观测性**: Lakeview提示词提供执行过程的可视化分析

这种设计确保了代理行为的一致性、可预测性和高质量输出。

---

## 提示词配置和管理

### 1. 工具注册表

**文件位置**: `trae_agent/tools/__init__.py`

**配置内容**:
```python
tools_registry: dict[str, Type[Tool]] = {
    "bash": BashTool,
    "str_replace_based_edit_tool": TextEditorTool,
    "json_edit_tool": JSONEditTool,
    "sequentialthinking": SequentialThinkingTool,
    "task_done": TaskDoneTool,
    "ckg": CKGTool,
}
```

### 2. TraeAgent工具配置

**文件位置**: `trae_agent/agent/trae_agent.py`

**配置内容**:
```python
TraeAgentToolNames = [
    "str_replace_based_edit_tool",
    "sequentialthinking",
    "json_edit_tool",
    "task_done",
    "bash",
]
```

**说明**: TraeAgent只使用工具注册表中的部分工具，CKG工具不在默认配置中。

---

## 提示词实际调用示例

### 1. 系统提示词调用

**调用位置**: `trae_agent/agent/base.py` 中的LLM对话构建

```python
# 构建LLM消息时的系统提示词使用
messages = [
    LLMMessage(role="system", content=self.get_system_prompt()),
    LLMMessage(role="user", content=user_input),
    # ... 其他消息
]
```

### 2. 工具描述调用

**调用位置**: LLM需要了解可用工具时

```python
# 构建工具信息
tools_info = []
for tool_name in self.tool_names:
    tool = self.tool_executor.get_tool(tool_name)
    tools_info.append({
        "name": tool.get_name(),
        "description": tool.get_description(),  # 这里使用工具描述提示词
        "parameters": tool.get_parameters()
    })
```

### 3. Lakeview提示词调用

**调用位置**: `trae_agent/utils/lake_view.py`

```python
# 任务提取
async def extract_task_in_step(self, prev_step: str, this_step: str):
    llm_messages = [
        LLMMessage(role="user", content=f"...{prev_step}...{this_step}..."),
        LLMMessage(role="assistant", content="I understand."),
        LLMMessage(role="user", content=EXTRACTOR_PROMPT),  # 使用提取提示词
        LLMMessage(role="assistant", content="Sure. Here is the task the agent is performing: <task>The agent"),
    ]

# 标签分类
async def extract_tag_in_step(self, step: str):
    llm_messages = [
        LLMMessage(role="user", content=f"...{steps_fmt}...{step}..."),
        LLMMessage(role="assistant", content="I understand."),
        LLMMessage(role="user", content=TAGGER_PROMPT),  # 使用标签提示词
        LLMMessage(role="assistant", content="Sure. The tags are: <tags>"),
    ]
```

---

## 提示词设计原则分析

### 1. 明确性原则

**体现**:
- 系统提示词明确定义角色："You are an expert AI software engineering agent"
- 工具描述具体说明功能和限制
- Lakeview提示词有明确的输出格式要求

**示例**:
```
# 明确的路径规则
File Path Rule: All tools that take a `file_path` as an argument require an **absolute path**.

# 明确的输出格式
Output your answer in two granularities: <task>...</task><details>...</details>.
```

### 2. 结构化原则

**体现**:
- 7步工作流程结构化
- 工具描述按功能分类组织
- Lakeview标签有预定义的分类体系

**示例**:
```
Follow these steps methodically:
1. Understand the Problem
2. Explore and Locate
3. Reproduce the Bug (Crucial Step)
4. Debug and Diagnose
5. Develop and Implement a Fix
6. Verify and Test Rigorously
7. Summarize Your Work
```

### 3. 约束性原则

**体现**:
- 强制使用绝对路径
- 限制输出长度（task标签最多10词）
- 要求验证后才能调用task_done

**示例**:
```
# 路径约束
Do NOT use relative paths like `src/main.py`.

# 输出约束
contain at most 10 words

# 行为约束
Note that you cannot call this tool before any verification is done.
```

### 4. 指导性原则

**体现**:
- 详细的sequential_thinking使用指南
- 具体的工具使用注意事项
- 明确的质量标准要求

**示例**:
```
# 工具使用指导
Your thinking should be thorough and so it's fine if it's very long. Set total_thoughts to at least 5, but setting it up to 25 is fine as well.

# 质量指导
Act like a senior software engineer. Prioritize correctness, safety, and high-quality, test-driven development.
```

---

## 提示词版本管理

### 1. 当前版本信息

- **系统提示词**: 无版本标识，直接在代码中定义
- **工具描述**: 与工具代码耦合，随工具版本更新
- **Lakeview提示词**: 固定在lake_view.py中

### 2. 修改历史追踪

由于提示词直接嵌入在代码中，其修改历史可以通过Git提交记录追踪：

```bash
# 查看系统提示词修改历史
git log -p trae_agent/prompt/agent_prompt.py

# 查看工具描述修改历史
git log -p trae_agent/tools/edit_tool.py

# 查看Lakeview提示词修改历史
git log -p trae_agent/utils/lake_view.py
```

---

## 提示词性能考虑

### 1. 长度优化

**系统提示词长度**: 约1500字符
- 包含完整的7步工作流程
- 详细的工具使用指导
- 适中的长度，不会超出大多数LLM的上下文限制

**工具描述长度**: 100-500字符不等
- 简洁明了，重点突出
- 包含关键使用注意事项

### 2. 缓存策略

由于提示词是静态的，可以在以下层面进行缓存：
- 系统提示词在Agent实例化时加载一次
- 工具描述在工具注册时生成一次
- Lakeview提示词作为常量定义

### 3. 性能影响

**Token消耗**:
- 系统提示词: ~400 tokens
- 工具描述总计: ~200 tokens
- Lakeview提示词: ~150 tokens per call

**调用频率**:
- 系统提示词: 每次对话开始时使用
- 工具描述: 每次需要工具信息时使用
- Lakeview提示词: 每个执行步骤后使用（如果启用）

---

## 提示词测试和验证

### 1. 功能测试

**系统提示词测试**:
- 验证7步工作流程是否被正确执行
- 检查绝对路径规则是否被遵循
- 确认质量标准是否被应用

**工具描述测试**:
- 验证LLM能否正确理解工具功能
- 检查工具调用参数是否正确
- 确认错误处理是否按描述执行

**Lakeview提示词测试**:
- 验证任务提取的准确性
- 检查标签分类的正确性
- 确认输出格式的一致性

### 2. 质量评估

**评估维度**:
- 准确性: 提示词是否产生预期行为
- 一致性: 相同输入是否产生一致输出
- 完整性: 是否覆盖所有必要场景
- 可理解性: LLM是否能正确理解指令

**评估方法**:
- 单元测试: 测试特定提示词的功能
- 集成测试: 测试提示词组合的效果
- 回归测试: 确保修改不破坏现有功能
- A/B测试: 比较不同提示词版本的效果

---

## 提示词扩展指南

### 1. 添加新工具

**步骤**:
1. 创建新工具类，继承自Tool基类
2. 实现`get_description()`方法，返回工具描述提示词
3. 在`tools_registry`中注册新工具
4. 如需要，在`TraeAgentToolNames`中添加工具名

**示例**:
```python
class NewTool(Tool):
    def get_description(self) -> str:
        return """
        新工具的描述提示词
        * 功能说明
        * 使用注意事项
        * 参数格式要求
        """
```

### 2. 修改系统提示词

**注意事项**:
- 保持7步工作流程的核心结构
- 确保绝对路径规则不被破坏
- 维护质量标准的要求
- 测试修改对现有功能的影响

### 3. 扩展Lakeview功能

**可扩展点**:
- 添加新的分析维度
- 增加新的标签类型
- 扩展输出格式
- 增加分析粒度

**示例**:
```python
# 添加新标签
KNOWN_TAGS = {
    # 现有标签...
    "NEW_TAG": "🆕",  # 新增标签
}

# 扩展TAGGER_PROMPT
TAGGER_PROMPT = """
# 现有内容...
NEW_TAG: It performs a new type of operation not covered by existing tags.
"""
```

---

## 常见问题和解决方案

### 1. 提示词不生效

**可能原因**:
- 提示词格式错误
- LLM模型不支持特定指令
- 上下文长度超限

**解决方案**:
- 检查提示词语法和格式
- 测试不同LLM模型的兼容性
- 优化提示词长度

### 2. 工具调用错误

**可能原因**:
- 工具描述不够清晰
- 参数格式说明不明确
- 错误处理指导不足

**解决方案**:
- 增加具体的使用示例
- 明确参数类型和格式要求
- 详细说明错误情况的处理方式

### 3. Lakeview分析不准确

**可能原因**:
- 提示词过于模糊
- 示例不够充分
- 输出格式要求不明确

**解决方案**:
- 增加更多具体示例
- 明确输出格式要求
- 调整提示词的指导性语言

---

## 最佳实践建议

### 1. 提示词设计

- **明确性**: 使用具体、明确的语言
- **结构化**: 采用清晰的层次结构
- **示例驱动**: 提供充分的使用示例
- **约束明确**: 清楚说明限制和要求

### 2. 版本管理

- **文档化**: 记录每次修改的原因和影响
- **测试**: 修改前后进行充分测试
- **回滚**: 保留回滚到之前版本的能力
- **监控**: 监控修改后的效果

### 3. 性能优化

- **长度控制**: 保持提示词简洁有效
- **缓存利用**: 合理使用缓存机制
- **批量处理**: 减少重复的提示词调用
- **监控分析**: 定期分析性能指标

---

*文档最后更新时间: 2025年1月15日*
*版本: v1.0.0*
